import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from './Button';

interface ErrorPlaceholderProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
  className?: string;
}

const ErrorPlaceholder: React.FC<ErrorPlaceholderProps> = ({
  title = 'Error',
  message = 'Something went wrong. Please try again.',
  onRetry,
  showRetryButton = true,
  className = '',
}) => {
  return (
    <div
      role="alert"
      className={`w-full border border-red-200 bg-red-50 text-red-800 rounded-md px-4 py-3 shadow-sm flex items-start gap-3 ${className}`}
    >
      <AlertCircle className="w-5 h-5 mt-0.5 text-red-500 shrink-0" />

      <div className="flex-1">
        <p className="font-medium">{title}</p>
        <p className="text-sm">{message}</p>
      </div>

      {showRetryButton && onRetry && (
        <Button
          onClick={onRetry}
          iconLeft={RefreshCw}
          className="bg-transparent hover:bg-red-100 text-red-600 text-sm px-2 py-1"
        >
          Retry
        </Button>
      )}
    </div>
  );
};

export default ErrorPlaceholder;
