import api from '../lib/axiosInstance';
export interface User {
  id: string;
  name: string;
  email: string;
  department: string;
  role: string;
  status: 'Active' | 'Inactive';
  lastLogin: string;
  company?: string;
  groups?: string[];
  manager?: {
    id: string;
    name: string;
  };
}

export interface UsersResponse {
  results: User[];
  count: number;
  next?: string;
  previous?: string;
}

export const downloadUserTemplate = async (): Promise<Blob> => {
  const response = await api.get('/api/core/users/download_template/', {
    responseType: 'blob',
  });
  return response.data;
};

export const bulkUploadUsers = async (file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post('/api/core/users/bulk_upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const getUsers = async (): Promise<UsersResponse> => {
  const response = await api.get('/api/core/users/');
  return response.data;
};
