import React from 'react';
import { motion } from 'framer-motion';

interface LoaderProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

const Loader: React.FC<LoaderProps> = ({
  size = 'md',
  text = 'Please wait...',
  className = ''
}) => {
  const sizeStyles = {
    sm: 'w-4 h-4',
    md: 'w-10 h-10',
    lg: 'w-14 h-14'
  };

  const textSizeStyles = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className={`flex flex-col items-center justify-center gap-4 py-6 ${className}`}>
      <motion.div
        className={`rounded-full border-4 border-gray-300 border-t-teal-500 ${sizeStyles[size]}`}
        animate={{ rotate: 360 }}
        transition={{
          repeat: Infinity,
          ease: 'linear',
          duration: 1.2
        }}
      />
      <motion.p
        className={`text-gray-600 font-semibold ${textSizeStyles[size]}`}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        {text}
      </motion.p>
    </div>
  );
};

export default Loader;
